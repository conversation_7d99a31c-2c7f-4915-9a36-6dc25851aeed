[2025-08-05 16:26:54] default.INFO: 单个学校WebSocket服务器启动 {"listen":"websocket://0.0.0.0:8791","pid":0} []
[2025-08-05 16:26:55] default.INFO: 获取爬虫登录状态 {"res":"{\"is_logged_in\":false,\"message\":\"登录将在后台执行，请稍后再次检查登录状态\",\"login_time\":null}"} []
[2025-08-05 16:26:55] default.INFO: 进程启动请求爬虫状态 {"pid":0,"timestamp":"2025-08-05 16:26:55"} []
[2025-08-05 16:27:46] default.INFO: 获取学生列表请求参数: {"name":"\u5f20","page":"1","limit":"10"} [] []
[2025-08-05 16:27:48] default.INFO: 获取学生列表请求参数: {"name":"\u5f20\u4e00","page":"1","limit":"10"} [] []
[2025-08-05 16:27:52] default.INFO: toggleAIOverlay 请求参数: {"name":"张一","sex":"1","phone":"15955304313","undergraduateSchool":624,"undergraduateSchoolName":"皖西学院","undergraduateMajor":32213,"undergraduateMajorName":"计算机科学与技术","disciplineCategory":8,"firstLevelDiscipline":128,"targetMajor":[3451],"targetMajorName":["(085400)电子信息"],"majorCode":"085400","examYear":"2026","isMultiDisciplinary":"2","educationalStyle":"0","mathScores":[{"id":1,"title":"英语","score":"80"},{"id":2,"title":"高数","score":"85"}],"undergraduateTranscript":[{"id":1,"title":"英语","score":"80"},{"id":2,"title":"高数","score":"85"}],"englishScore":"120","cet4":"480","cet6":"495","tofelScore":"","ieltsScore":"","englishAbility":"一般","targetRegion":"A区","region":"A区","targetProvinces":"安徽省,浙江省,江苏省,上海市","targetSchool":14,"targetSchoolName":"中国科学技术大学","schoolLevel":["211"],"referenceBooks":"","politics":"70","englishType":"110","englishS":"75","mathType":"120","mathScore":"","professionalScore":"","totalScore":"375","personalNeeds":"没有","weakModules":"","student_id":43} [] []
[2025-08-05 16:27:52] default.INFO: 四级成绩: 480 [] []
[2025-08-05 16:27:52] default.INFO: 六级成绩: 495 [] []
[2025-08-05 16:27:52] default.INFO: 托福成绩:  [] []
[2025-08-05 16:27:52] default.INFO: 英语能力: 一般 [] []
[2025-08-05 16:27:52] default.INFO: 地区倾向: A区 [] []
[2025-08-05 16:27:52] default.INFO: inputMajorCodes:085400 [] []
[2025-08-05 16:27:52] default.INFO: dreamSchoolInfoSql: SELECT * FROM `ba_school_info` WHERE  `school_name` = '中国科学技术大学'  AND `major_code` = '085400' LIMIT 1 [] []
[2025-08-05 16:27:52] default.INFO: 院校数量: 786 [] []
[2025-08-05 16:27:52] default.INFO: 院校数量: 13 [] []
[2025-08-05 16:27:52] default.INFO: 调用爬虫接口 - URL: http://127.0.0.1:8000/api/crawl_school_info_sync_limited [] []
[2025-08-05 16:27:52] default.INFO: 调用爬虫接口 - 参数: {"ids":[84940,84943,84944,84949,26714,26753,26791,19398,19399,19427,19406,19435,19436]} [] []
[2025-08-05 16:28:08] default.INFO: 爬虫接口调用成功 - 响应: {"results":[{"school_id":84943,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":84940,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":84949,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":26714,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":84944,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":26753,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19399,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19398,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19427,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":26791,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19406,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19435,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null},{"school_id":19436,"status":"skipped","message":"学校信息已爬取，跳过处理","data_summary":null}]} [] []
[2025-08-05 16:28:09] default.INFO: 院校数量: 22 [] []
[2025-08-05 16:28:10] default.INFO: 一级学科代码: 128, 对应的专业代码: 0854 [] []
[2025-08-05 16:28:10] default.INFO: 获取国家线数据请求参数: {"firstLevelDisciplineCode":"0854"} [] []
[2025-08-05 16:28:10] default.INFO: 流式AI推荐请求参数: {"report_id":"786"} [] []
[2025-08-05 16:28:10] default.INFO: context: "学生基本信息：\n姓名：张一\n性别：男\n本科院校：皖西学院\n本科专业：计算机科学与技术\n培养方式：全日制\n是否跨专业：否\n本科成绩：\n英语：80分\n高数：85分\n\n英语基础：\n高考英语成绩：120分\n大学四级成绩：480分\n大学六级成绩：495分\n英语能力：一般\n\n考试成绩预估：\n政治：70分\n英语：75分\n业务课一：110分\n业务课二：120分\n专业课：分\n总分：375分\n\n目标偏好：\n目标区域：A区\n目标省份：安徽省,浙江省,江苏省,上海市\n院校层次：211\n梦想院校：中国科学技术大学\n个性化需求：没有\n学校列表：\n学校名称: 上海大学\n专业名称: 085400\n学院名称: 通信与信息工程学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 信号系统与电路\n初试参考书: (829)信号系统与电路:《信号与系统》（上、下册）（第3版）郑君里等 高等教育出版社 2011年。 《电路基础》（第四版）王松林，吴大正，李小平，王辉 西安电子科技大学出版社 2021年 。;(302)数学（二）:统考;\n复试内容: 复试内容：复试科目：通信原理；《通信原理》（第7版） 樊昌信等编 国防工业出版社 2012年;；\n招生人数：\n学校名称: 上海大学\n专业名称: 085400\n学院名称: 机电工程与自动化学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 自动控制理论（含经典和现代）\n初试参考书: (836)自动控制理论（含经典和现代）:《自动控制原理》（第7版）胡寿松 科学出版社 2019年，《现代控制理论》贾立 邵定国 沈天飞编 上海大学出版社 2013年;(302)数学（二）:统考;\n复试内容: 复试内容：复试科目：微机硬件及软件（包含8086和C语言）；《微机原理与接口技术》（第2版）杨帮华等 清华大学出版社 2013年，《微型计算机技术》（第四版）孙德文 章鸣嬛著 高等教育出版社 2018年 ，《C程序设计》(第五版) 谭浩强 清华大学出版社 2017年;；\n招生人数：\n学校名称: 上海大学\n专业名称: 085400\n学院名称: 上海电影学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 图形图像技术（专）\n初试参考书: (875)图形图像技术（专）:《数字图像处理MATLAB版》(第2版)冈萨雷斯等著阮秋琦译电子工业出版社2014年；《计算机图形学基础教程(Visual C++)》孔令德编著 清华大学出版社 2013年；;(302)数学（二）:统考;\n复试内容: 复试内容：复试科目：影视信息处理综合不指定参考书目;；\n招生人数：\n学校名称: 东华大学\n专业名称: 085400\n学院名称: 信息科学与技术学院\n初试考试科目: 思想政治理论,英语（二）, 数学（一）, 信号与系统\n初试参考书: (301)数学（一）:统考;(824)自动控制理论:《现代控制理论》刘豹，唐万生主编，机械工业出版社，第三版，2006； 《自动控制原理》(第五版)，胡寿松，科学出版社，2007； 《工程控制基础》，田作华，清华大学出版社，2007。;(836)信号与系统:《信号与线性系统（第五版）》，管致中，夏恭恪，孟桥，北京：高等教育出版社，2017； 《信号与线性系统》白恩健，吴贇等，北京：电子工业出版社，2019。;\n复试内容: 复试内容：未知;；\n招生人数：\n学校名称: 南京农业大学\n专业名称: 085400\n学院名称: 人工智能学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 计算机学科专业基础\n初试参考书: (408)计算机学科专业基础:;(302)数学（二）:;(829)电路:《电路》，原著邱关源，主编罗先觉，高等教育出版社，第6版，2022年;\n复试内容: 复试内容：01方向复试科目:1902 自动控制原理（I、II）或1903 数字信号处理——1902 自动控制原理（I、II）——胡寿松《自动控制原理》（第7版）（经典控制理论部分，1-7章），张嗣瀛，高立群，编著《现代控制理论》（第2版，1-6章）。1903 数字信号处理——高西全，丁玉美 编著，《数字信号处理》第4版，西安电子科技大学出版社。02方向复试科目:1901 数据库系统原理、C程序设计——（数据库笔试100分，C程序上机50分）数据库系数统概论（第6版），王珊，杜小勇，陈红，高等教育出版社；C语言程序设计（第4版），何钦铭，颜晖，高等教育出版社。;；\n招生人数：\n学校名称: 南京航空航天大学\n专业名称: 085400\n学院名称: 电子信息工程学院\n初试考试科目: 思想政治理论,英语（一）, 数学（二）, 数字电路和信号与系统\n初试参考书: (302)数学（二）:;(878)数字电路和信号与系统:刘祝华，数字电子技术（第2版），北京：电子工业出版社，2020.7。朱钢，黎宁等，信号与系统，北京：高等教育出版社，2024。;\n复试内容: 复试内容：复试科目：①545信息与通信工程专业综合；参考书目：《通信原理（第7版）》樊昌信 曹丽娜 编，国防工业出版社，2015年6月。《现代模拟电子技术基础（第3版）》，王成华、胡志忠、邵杰、洪峰、刘伟强编，北京航空航天大学出版社，2020年。;；\n招生人数：\n学校名称: 南京航空航天大学\n专业名称: 085400\n学院名称: 航天学院\n初试考试科目: 思想政治理论（单独考试）,英语（单独考试）, 高等数学（单独考试）, 普通物理\n初试参考书: (302)数学（二）:;(811)普通物理:1. 《普通物理学》（第六版），程守洙、江之永主编，高等教育出版社。2. 《物理学》（第五版），东南大学等七所工科院校编，马文蔚等改编，高等教育出版社;\n复试内容: 复试内容：复试科目：①598光电信息工程基础或②599控制技术综合。【598光电信息工程基础参考书目】：[1] 郁道银、谈恒英，《工程光学（第4版）》，机械工业出版社，2016年。[2] 樊昌信等，《通信原理（第七版）》，国防工业出版社，2018年。[3] 贾永红，《数字图像处理（第3版）》武汉大学出版社，2016年。[4] 蔡利梅、王利娟，《数字图像处理——使用MATLAB分析与实现》 清华大学出版社，2019年。【599控制技术综合参考书目录】：[1] 潘双来，邢丽冬. 电路理论基础(第三版)，清华大学出版社，2016 年。[2] 张涛、王学谦、刘宜成.《航天器控制基础》，清华大学出版社，2020年。[3] 吴宁等，《微型计算机原理与接口技术(第4版)》， 清华大学出版社，2016年。;；\n招生人数：\n学校名称: 南京航空航天大学\n专业名称: 085400\n学院名称: 集成电路学院\n初试考试科目: 思想政治理论（单独考试）,英语（单独考试）, 高等数学（单独考试）, 数字电路和信号与系统\n初试参考书: (302)数学（二）:;(878)数字电路和信号与系统:刘祝华，数字电子技术（第2版），北京：电子工业出版社，2020.7。朱钢，黎宁等，信号与系统，北京：高等教育出版社，2024。;\n复试内容: 复试内容：复试科目：①545信息与通信工程专业综合；参考书目：《通信原理（第7版）》樊昌信 曹丽娜 编，国防工业出版社，2015年6月。《现代模拟电子技术基础（第3版）》，王成华、胡志忠、邵杰、洪峰、刘伟强编，北京航空航天大学出版社，2020年。;；\n招生人数：\n学校名称: 合肥工业大学\n专业名称: 085400\n学院名称: 电气与自动化工程学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 自动控制理论基础\n初试参考书: (302)数学（二）((009:;(832)自动控制理论基础:《自动控制理论》，王孝武、方敏、葛锁良，机械工业出版社，2009《现代控制理论基础》（第 3 版），王孝武，机械工业出版社，2013《自动控制原理》（第七版），胡寿松，科学出版社，2019;\n复试内容: 复试内容：①0047控制工程基础【传感器与检测技术《传感器与检测技术》（第四版），徐科军主编，电子工业出版社，2016 年；C 语言程序设计《C 语言程序设计》（第四版），苏小红、赵玲玲等编著，高等教育出版社，2019 年】;；\n招生人数：\n学校名称: 合肥工业大学\n专业名称: 085400\n学院名称: 物理学院\n初试考试科目: 思想政治理论,英语（二）, 数学（一）, 半导体物理\n初试参考书: (301)数学（一）((005:;(868)半导体物理:《半导体物理学》（第7版），刘恩科、朱秉升、罗晋生，电子工业出版社，2017;\n复试内容: 复试内容：①0184电子信息技术综合【模拟电子技术《模拟电子技术基础》，华成英、童诗白，高等教育出版社出版，2015；数字电路《数字电子技术基础》，阎石，高等教育出版社，2006；《数字集成电路—电路、系统与设计（第二版）》，Jan M.Rabaey 著，周润德译，电子工业出版社，2015】;；\n招生人数：\n学校名称: 安徽大学\n专业名称: 085400\n学院名称: 联合培养（中科院合肥物质科学研究院）\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 计算机学科专业基础\n初试参考书: (302)数学（二）((024:;(408)计算机学科专业基础((023:;\n复试内容: 复试内容：F67计算机专业综合（数据库原理、高级语言程序设计）：数据库原理包含：数据库基础知识；数据模型与概念模型；数据库系统的设计方法；关系数据库；关系数据库标准语言；关系数据库理论；数据库保护技术；新型数据库系统及数据库技术的发展等。高级语言程序设计包含：C程序基本结构，基本数据类型，数组的定义及引用；函数的定义及调用；局部变量和全局变量；变量的存储类别；指针；结构体等。;；\n招生人数：\n学校名称: 河海大学\n专业名称: 085400\n学院名称: 计算机与软件学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 计算机学科专业基础\n初试参考书: (302)数学（二）:;(408)计算机学科专业基础:;\n复试内容: 复试内容：040003 程序设计:请参考相应的本科专业通用教材，考试范围为相关领域本科阶段专业基础课的基本知识点。;；\n招生人数：\n学校名称: 河海大学\n专业名称: 085400\n学院名称: 人工智能与自动化学院\n初试考试科目: 思想政治理论,英语（二）, 数学（二）, 人工智能专业基础\n初试参考书: (302)数学（二）:;(827)自动控制理论基础:《自动控制原理》（第七版），胡寿松主编，科学出版社，2019 年；《现代控制理论》（第 3 版），王宏华主编，电子工业出版社，2018 年。;\n复试内容: 复试内容：03 方向：①043001 控制工程综合04 方向：①043002 人工智能综合043001 控制工程综合:《微型计算机原理与接口技术》（第 2 版），邹逢兴主编，清华大学出版社，2016 年；《程序设计基础教程》（C 语言描述）（第二版），丁海军、金永霞编著，清华大学出版社，2013年。043002 人工智能综合:《人工智能原理及其应用》（第 4 版），王万森著，电子工业出版社，2018 年；《机器学习》，周志华著，清华大学出版社，2016 年。;；\n招生人数：\n\n" [] []
