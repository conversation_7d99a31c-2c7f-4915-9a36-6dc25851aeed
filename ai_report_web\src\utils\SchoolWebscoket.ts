import {useUserStore} from "@/store/modules/user"
const userStore = useUserStore()
export enum ModuleType {
    score_formula="score_formula",
    competition_analysis="competition_analysis",
    study_suggestions="study_suggestions",
}
export interface requestData{
    report_id:string;
    school_name:string;
    college_name:string;
    module_type: ModuleType;
}
interface recieveData{
    content:string;
    is_final:boolean;
}
type EventHandler = (data:any)=>void
type ErrorHandler = (error:any)=>void;
class SchoolWebSocket {
    private socket:WebSocket|null = null;
    private url:string="";
    private heartbeatInterval:number = 1000;
    private heartbeatTimer:ReturnType<typeof setInterval>|null = null;
    private connectionTimeout:number|null = null;
    private reconnectInterval:number = 6000;
    private reconnectTimer:ReturnType<typeof setInterval>|null = null;
    private messageHandler:EventHandler|null=null;
    private openHandler:EventHandler|null=null;
    private errorHandler:<PERSON>rror<PERSON>and<PERSON>|null=null;
    private connectionHandler:EventHandler|null=null;
    private disconnectionHandler:EventHandler|null=null;
    private token:string="";

    constructor(url:string,heartbeatInterval:string, reconnectInterval:string, token:string) {
        this.url = url;
        this.heartbeatInterval = parseInt(heartbeatInterval);
        this.reconnectInterval = parseInt(reconnectInterval);
        this.token = token;
    }
    public connect() {
        this.socket = new WebSocket(this.url);
        this.socket.onopen = (event) => {
            console.log("socket 连接成功")
            this.openHandler?.(event);
            this.sendHeartbeat()
        };
        this.socket.onmessage = (event) => {
            console.log("socket 收到消息",event)
            this.messageHandler?.(event);
        };
        this.socket.onerror = (event) => {
            console.log("socket 连接错误",event)
            this.stopHeartbeat()
            this.errorHandler?.(event);
        };
        this.socket.onclose = (event) => {
            console.log("socket 连接关闭",event)
            this.stopHeartbeat()
            this.disconnectionHandler?.(event);
            this.reconnect()
        };
    }
    public send(data:requestData){
        if(this.socket && this.socket.readyState===WebSocket.OPEN){
            //token 带上
            this.socket.send(JSON.stringify({...data, token:this.token}));
        }else{
            console.log("当前socket未连接")
        }
    }
    public onopen(callback:EventHandler) {
        this.openHandler = callback;
    }
    public onmessage(callback:EventHandler) {
        this.messageHandler = callback;
    }
    public onerror(callback:ErrorHandler) {
        this.errorHandler = callback;
    }
    public onclose(callback:EventHandler) {
        this.disconnectionHandler = callback;
    }
    //心跳
    public startHeartbeat(){
        this.heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatInterval);
    }

    public sendHeartbeat(){
        if(this.socket){
            this.socket.send("ping");
        }
    }

    public stopHeartbeat(){
        if(this.heartbeatTimer){
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    public reconnect(){
        if(this.reconnectTimer) return
        this.reconnectTimer = setTimeout(() => {
            this.connect();
            this.reconnectTimer = null;
        }, this.reconnectInterval);
    }
     get Socket(){
        return this.socket
    }

}
//初始化三个 websocket 连接， task 取出
export class WebSocketManager{
    private connections: SchoolWebSocket[] = [];
    private currentIndex: number = 0;
    private readonly maxConnections: number = 3;
    private readonly url: string = 'ws://127.0.0.1:8788';
    private readonly heartbeatInterval: string = '1000';
    private readonly reconnectInterval: string = '6000';
    private isInitialized: boolean = false;

    constructor() {
        this.initConnections();
    }

    // 初始化三个WebSocket连接
    private initConnections() {
        for (let i = 0; i < this.maxConnections; i++) {
            const socket = new SchoolWebSocket(
                this.url,
                this.heartbeatInterval,
                this.reconnectInterval,
                userStore.token
            );

            // 设置连接事件处理
            socket.onopen(() => {
                console.log(`WebSocket连接${i + 1}已建立`);
                this.checkAllConnected();
            });

            socket.onerror((error) => {
                console.error(`WebSocket连接${i + 1}发生错误:`, error);
            });

            socket.onclose(() => {
                console.log(`WebSocket连接${i + 1}已关闭`);
            });

            this.connections.push(socket);
            socket.connect();
        }
    }

    // 检查所有连接是否都已建立
    private checkAllConnected() {
        const connectedCount = this.connections.filter(
            socket => socket.Socket && socket.Socket.readyState === WebSocket.OPEN
        ).length;

        if (connectedCount === this.maxConnections && !this.isInitialized) {
            this.isInitialized = true;
            console.log('所有WebSocket连接已建立完成');
        }
    }

    // 获取下一个可用的连接（轮询方式）
    public getAvailableConnection(): SchoolWebSocket | null {
        if (!this.isInitialized) {
            console.warn('WebSocket连接池尚未完全初始化');
        }

        // 从当前索引开始查找可用连接
        for (let i = 0; i < this.maxConnections; i++) {
            const index = (this.currentIndex + i) % this.maxConnections;
            const socket = this.connections[index];

            if (socket['socket'] && socket['socket'].readyState === WebSocket.OPEN) {
                this.currentIndex = (index + 1) % this.maxConnections;
                return socket;
            }
        }

        console.warn('没有可用的WebSocket连接');
        return null;
    }

    // 获取连接状态
    public getConnectionStatus() {
        return this.connections.map((socket, index) => ({
            index: index + 1,
            status: socket['socket'] ? socket['socket'].readyState : 'Not Connected',
            isOpen: socket['socket'] && socket['socket'].readyState === WebSocket.OPEN
        }));
    }

    // 关闭所有连接
    public closeAllConnections() {
        this.connections.forEach((socket, index) => {
            if (socket['socket']) {
                socket['socket'].close();
                console.log(`WebSocket连接${index + 1}已关闭`);
            }
        });
        this.isInitialized = false;
    }
}

// 创建全局WebSocket管理器实例
export const wsManager = new WebSocketManager();

export const task = (data:requestData, callback:(data:any)=>void) => {
    return new Promise((resolve, reject) => {
        const socket = wsManager.getAvailableConnection();

        if (!socket) {
            reject(new Error('没有可用的WebSocket连接'));
            return;
        }

        // 设置消息处理器
        socket.onmessage((event) => {
            const data =  JSON.parse(event.data) as recieveData;
            console.log('收到消息', data);
            if(data.is_final){
                resolve(event.data);
            }else{
                callback(data.content);
            }
        });

        // 设置错误处理器
        socket.onerror((error) => {
            console.error('WebSocket发生错误:', error);
            reject(error);
        });

        // 发送数据
        socket.send(data);
    });
}